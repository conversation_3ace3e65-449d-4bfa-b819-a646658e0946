package com.redbook.kid;

import com.redbook.kid.system.config.PuzzleGameConfig;
import com.redbook.kid.system.model.dto.puzzle.request.LevelResultRequestDTO;
import com.redbook.kid.system.model.dto.puzzle.response.LevelResultResponseDTO;
import com.redbook.kid.system.model.entity.PuzzleUserProgressDO;
import com.redbook.kid.system.service.LollipopRecordService;
import com.redbook.kid.system.service.business.PuzzleService;
import com.redbook.kid.system.service.puzzle.PuzzleUserProgressService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 拼图奖励防重复发放测试
 */
@SpringBootTest
public class PuzzleRewardDuplicationTest {

    @Autowired
    private PuzzleService puzzleService;

    @SpyBean
    private PuzzleUserProgressService puzzleUserProgressService;

    @MockBean
    private LollipopRecordService lollipopRecordService;

    private static final String TEST_USER_ID = "test_user_001";
    private static final Integer TEST_LEVEL = 1;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        cleanupTestData();
    }

    /**
     * 测试限时突破奖励防重复发放
     */
    @Test
    void testQuickCompletionRewardNoDuplication() {
        // 第一次完成关卡，满足限时突破条件
        LevelResultRequestDTO request1 = createLevelResultRequest(TEST_LEVEL, 15, true, true); // 15秒完成，满足限时突破
        LevelResultResponseDTO response1 = puzzleService.submitLevelResult(request1);
        
        // 验证第一次获得了限时突破奖励
        int expectedReward1 = PuzzleGameConfig.getLevelConfig(TEST_LEVEL).getCandyReward() + 
                             PuzzleGameConfig.QUICK_COMPLETION_REWARD;
        assertEquals(expectedReward1, response1.getCandyReward());
        
        // 验证奖励状态已标记
        PuzzleUserProgressDO progress = puzzleUserProgressService.getLevelStatus(TEST_USER_ID, TEST_LEVEL);
        assertTrue(progress.getQuickCompletionRewardReceived());

        // 第二次完成同一关卡，同样满足限时突破条件
        LevelResultRequestDTO request2 = createLevelResultRequest(TEST_LEVEL, 10, true, true); // 10秒完成，仍满足限时突破
        LevelResultResponseDTO response2 = puzzleService.submitLevelResult(request2);
        
        // 验证第二次没有获得限时突破奖励
        int expectedReward2 = PuzzleGameConfig.getLevelConfig(TEST_LEVEL).getCandyReward(); // 只有基础奖励
        assertEquals(expectedReward2, response2.getCandyReward());
    }

    /**
     * 测试连续通关奖励防重复发放
     */
    @Test
    void testConsecutiveCompletionRewardNoDuplication() {
        // 模拟用户连续完成3关（都是完美通关）
        for (int level = 1; level <= 3; level++) {
            LevelResultRequestDTO request = createLevelResultRequest(level, 30, true, true);
            LevelResultResponseDTO response = puzzleService.submitLevelResult(request);

            if (level == 3) {
                // 第3关应该获得连续通关奖励（因为最近3关都是完美通关）
                int expectedReward = PuzzleGameConfig.getLevelConfig(level).getCandyReward() +
                                   PuzzleGameConfig.CONSECUTIVE_COMPLETION_REWARD;
                assertEquals(expectedReward, response.getCandyReward());

                // 验证里程碑已更新
                int milestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(TEST_USER_ID);
                assertEquals(3, milestone);
            }
        }

        // 再次完成第3关
        LevelResultRequestDTO request = createLevelResultRequest(3, 25, true, true);
        LevelResultResponseDTO response = puzzleService.submitLevelResult(request);

        // 验证没有重复获得连续通关奖励（因为有效连续数为0）
        int expectedReward = PuzzleGameConfig.getLevelConfig(3).getCandyReward(); // 只有基础奖励
        assertEquals(expectedReward, response.getCandyReward());
    }

    /**
     * 测试同时满足两种奖励条件的情况
     */
    @Test
    void testBothRewardsFirstTime() {
        // 模拟用户连续完成前2关
        for (int level = 1; level <= 2; level++) {
            LevelResultRequestDTO request = createLevelResultRequest(level, 30, true, true);
            puzzleService.submitLevelResult(request);
        }

        // 第3关：既满足限时突破，又满足连续通关
        LevelResultRequestDTO request = createLevelResultRequest(3, 15, true, true); // 15秒完成
        LevelResultResponseDTO response = puzzleService.submitLevelResult(request);
        
        // 验证同时获得两种奖励
        int expectedReward = PuzzleGameConfig.getLevelConfig(3).getCandyReward() + 
                           PuzzleGameConfig.QUICK_COMPLETION_REWARD + 
                           PuzzleGameConfig.CONSECUTIVE_COMPLETION_REWARD;
        assertEquals(expectedReward, response.getCandyReward());
        
        // 验证两种奖励状态都已标记
        PuzzleUserProgressDO progress = puzzleUserProgressService.getLevelStatus(TEST_USER_ID, 3);
        assertTrue(progress.getQuickCompletionRewardReceived());

        // 验证连续通关里程碑已更新
        int milestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(TEST_USER_ID);
        assertEquals(3, milestone);
    }

    /**
     * 测试您提到的具体场景：跳关后补做的连续通关奖励
     */
    @Test
    void testSkipLevelsAndBackfillScenario() {
        // 场景：用户跳过1,2关，直接完成3,4,5关
        for (int level = 3; level <= 5; level++) {
            LevelResultRequestDTO request = createLevelResultRequest(level, 30, true, true);
            LevelResultResponseDTO response = puzzleService.submitLevelResult(request);

            if (level == 5) {
                // 第5关时，总完成数=3，里程碑=0，有效连续数=3，应该获得奖励
                int expectedReward = PuzzleGameConfig.getLevelConfig(level).getCandyReward() +
                                   PuzzleGameConfig.CONSECUTIVE_COMPLETION_REWARD;
                assertEquals(expectedReward, response.getCandyReward());

                // 验证里程碑更新为3
                int milestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(TEST_USER_ID);
                assertEquals(3, milestone);
            }
        }

        // 完成第6关
        LevelResultRequestDTO request6 = createLevelResultRequest(6, 30, true, true);
        LevelResultResponseDTO response6 = puzzleService.submitLevelResult(request6);

        // 第6关时，总完成数=4，里程碑=3，有效连续数=1，不应该获得奖励
        int expectedReward6 = PuzzleGameConfig.getLevelConfig(6).getCandyReward();
        assertEquals(expectedReward6, response6.getCandyReward());

        // 回去补做第1关
        LevelResultRequestDTO request1 = createLevelResultRequest(1, 30, true, true);
        LevelResultResponseDTO response1 = puzzleService.submitLevelResult(request1);

        // 第1关时，总完成数=5，里程碑=3，有效连续数=2，不应该获得奖励
        int expectedReward1 = PuzzleGameConfig.getLevelConfig(1).getCandyReward();
        assertEquals(expectedReward1, response1.getCandyReward());

        // 补做第2关（完美通关）
        LevelResultRequestDTO request2 = createLevelResultRequest(2, 30, true, true);
        LevelResultResponseDTO response2 = puzzleService.submitLevelResult(request2);

        // 第2关时，总完成数=6，里程碑=3，有效连续数=3，且第2关是时间上最后完成的完美通关，应该获得奖励
        int expectedReward2 = PuzzleGameConfig.getLevelConfig(2).getCandyReward() +
                             PuzzleGameConfig.CONSECUTIVE_COMPLETION_REWARD;
        assertEquals(expectedReward2, response2.getCandyReward());

        // 验证里程碑更新为6
        int finalMilestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(TEST_USER_ID);
        assertEquals(6, finalMilestone);
    }

    /**
     * 测试连续通关奖励：最近3关必须都是完美通关
     */
    @Test
    void testConsecutiveRewardRequiresAllPerfect() {
        // 完成第1关（完美通关）
        LevelResultRequestDTO request1 = createLevelResultRequest(1, 30, true, true);
        puzzleService.submitLevelResult(request1);

        // 完成第2关（非完美通关）
        LevelResultRequestDTO request2 = createLevelResultRequest(2, 30, false, true);
        puzzleService.submitLevelResult(request2);

        // 完成第3关（完美通关）
        LevelResultRequestDTO request3 = createLevelResultRequest(3, 30, true, true);
        LevelResultResponseDTO response3 = puzzleService.submitLevelResult(request3);

        // 第3关时，总完成数=3，里程碑=0，有效连续数=3，但最近3关不全是完美通关，不应该获得奖励
        int expectedReward3 = PuzzleGameConfig.getLevelConfig(3).getCandyReward();
        assertEquals(expectedReward3, response3.getCandyReward());

        // 验证里程碑没有更新
        int milestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(TEST_USER_ID);
        assertEquals(0, milestone);

        // 继续完成第4关（完美通关）
        LevelResultRequestDTO request4 = createLevelResultRequest(4, 30, true, true);
        puzzleService.submitLevelResult(request4);

        // 完成第5关（完美通关）
        LevelResultRequestDTO request5 = createLevelResultRequest(5, 30, true, true);
        puzzleService.submitLevelResult(request5);

        // 完成第6关（完美通关）
        LevelResultRequestDTO request6 = createLevelResultRequest(6, 30, true, true);
        LevelResultResponseDTO response6 = puzzleService.submitLevelResult(request6);

        // 第6关时，总完成数=6，里程碑=0，有效连续数=6，且最近3关（4,5,6）都是完美通关，应该获得奖励
        int expectedReward6 = PuzzleGameConfig.getLevelConfig(6).getCandyReward() +
                             PuzzleGameConfig.CONSECUTIVE_COMPLETION_REWARD;
        assertEquals(expectedReward6, response6.getCandyReward());

        // 验证里程碑更新为6
        int finalMilestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(TEST_USER_ID);
        assertEquals(6, finalMilestone);
    }

    /**
     * 测试最后一次通关不是完美通关的情况
     */
    @Test
    void testConsecutiveRewardWithNonPerfectLastCompletion() {
        // 完成前2关（完美通关）
        for (int level = 1; level <= 2; level++) {
            LevelResultRequestDTO request = createLevelResultRequest(level, 30, true, true);
            puzzleService.submitLevelResult(request);
        }

        // 完成第3关（非完美通关）
        LevelResultRequestDTO request3 = createLevelResultRequest(3, 30, false, true);
        LevelResultResponseDTO response3 = puzzleService.submitLevelResult(request3);

        // 第3关时，总完成数=3，里程碑=0，有效连续数=3，但最后一次通关不是完美通关，不应该获得奖励
        int expectedReward3 = PuzzleGameConfig.getLevelConfig(3).getCandyReward();
        assertEquals(expectedReward3, response3.getCandyReward());

        // 验证里程碑没有更新
        int milestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(TEST_USER_ID);
        assertEquals(0, milestone);
    }

    /**
     * 测试您提到的具体场景：先非完美通关，后重新完美通关
     */
    @Test
    void testReplayLevelsForPerfectCompletion() {
        // 1. 用户完成1,2关（未完美通关）
        LevelResultRequestDTO request1 = createLevelResultRequest(1, 30, false, true);
        puzzleService.submitLevelResult(request1);

        LevelResultRequestDTO request2 = createLevelResultRequest(2, 30, false, true);
        puzzleService.submitLevelResult(request2);

        // 2. 用户完成3,4,5关（完美通关）
        for (int level = 3; level <= 5; level++) {
            LevelResultRequestDTO request = createLevelResultRequest(level, 30, true, true);
            LevelResultResponseDTO response = puzzleService.submitLevelResult(request);

            if (level == 5) {
                // 第5关时，最近3关是3,4,5，都是完美通关，应该获得奖励
                int expectedReward = PuzzleGameConfig.getLevelConfig(level).getCandyReward() +
                                   PuzzleGameConfig.CONSECUTIVE_COMPLETION_REWARD;
                assertEquals(expectedReward, response.getCandyReward());

                // 验证里程碑更新为5
                int milestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(TEST_USER_ID);
                assertEquals(5, milestone);
            }
        }

        // 3. 用户完成第6关（完美通关）
        LevelResultRequestDTO request6 = createLevelResultRequest(6, 30, true, true);
        LevelResultResponseDTO response6 = puzzleService.submitLevelResult(request6);

        // 第6关时，总完成数=6，里程碑=5，有效连续数=1，不应该获得奖励
        int expectedReward6 = PuzzleGameConfig.getLevelConfig(6).getCandyReward();
        assertEquals(expectedReward6, response6.getCandyReward());

        // 4. 用户重新游玩第1关（完美通关）
        LevelResultRequestDTO replayRequest1 = createLevelResultRequest(1, 30, true, true);
        LevelResultResponseDTO replayResponse1 = puzzleService.submitLevelResult(replayRequest1);

        // 重新游玩第1关，总完成数还是6，有效连续数=1，不应该获得奖励
        int expectedReplayReward1 = PuzzleGameConfig.getLevelConfig(1).getCandyReward();
        assertEquals(expectedReplayReward1, replayResponse1.getCandyReward());

        // 5. 用户重新游玩第2关（完美通关）
        LevelResultRequestDTO replayRequest2 = createLevelResultRequest(2, 30, true, true);
        LevelResultResponseDTO replayResponse2 = puzzleService.submitLevelResult(replayRequest2);

        // 重新游玩第2关时，总完成数还是6，里程碑=5，有效连续数=1，不应该获得奖励
        // 但是现在最近3关是6,1,2，都是完美通关，但有效连续数不满足条件
        int expectedReplayReward2 = PuzzleGameConfig.getLevelConfig(2).getCandyReward();
        assertEquals(expectedReplayReward2, replayResponse2.getCandyReward());

        // 验证里程碑没有变化
        int finalMilestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(TEST_USER_ID);
        assertEquals(5, finalMilestone);
    }

    /**
     * 测试数据库并发安全性
     */
    @Test
    void testConcurrentRewardSafety() {
        // 这个测试需要在实际环境中进行，这里只是示例结构
        // 可以使用多线程同时提交同一关卡的完成结果，验证只有一次能获得奖励

        LevelResultRequestDTO request = createLevelResultRequest(TEST_LEVEL, 15, true, true);

        // 模拟并发调用
        LevelResultResponseDTO response1 = puzzleService.submitLevelResult(request);
        LevelResultResponseDTO response2 = puzzleService.submitLevelResult(request);

        // 验证只有一次获得了额外奖励
        int baseReward = PuzzleGameConfig.getLevelConfig(TEST_LEVEL).getCandyReward();
        int rewardWithBonus = baseReward + PuzzleGameConfig.QUICK_COMPLETION_REWARD;

        // 其中一个应该有奖励，另一个应该只有基础奖励
        assertTrue((response1.getCandyReward() == rewardWithBonus && response2.getCandyReward() == baseReward) ||
                  (response1.getCandyReward() == baseReward && response2.getCandyReward() == rewardWithBonus));
    }

    private LevelResultRequestDTO createLevelResultRequest(Integer levelNumber, Integer completionTime, 
                                                          Boolean isPerfect, Boolean isCompleted) {
        LevelResultRequestDTO request = new LevelResultRequestDTO();
        request.setUserId(TEST_USER_ID);
        request.setLevelNumber(levelNumber);
        request.setCompletionTime(completionTime);
        request.setIsPerfect(isPerfect);
        request.setIsCompleted(isCompleted);
        request.setCompletedWordIds(Arrays.asList(1, 2, 3)); // 模拟完成的单词
        return request;
    }

    private void cleanupTestData() {
        // 清理测试用户的进度数据
        // 这里需要根据实际的数据访问层实现
    }
}
