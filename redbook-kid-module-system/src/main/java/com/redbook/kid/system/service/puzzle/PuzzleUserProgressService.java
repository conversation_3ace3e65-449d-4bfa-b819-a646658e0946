package com.redbook.kid.system.service.puzzle;

import top.continew.starter.extension.crud.service.BaseService;
import top.continew.starter.data.mp.service.IService;
import com.redbook.kid.system.model.query.PuzzleUserProgressQuery;
import com.redbook.kid.system.model.req.PuzzleUserProgressReq;
import com.redbook.kid.system.model.resp.PuzzleUserProgressDetailResp;
import com.redbook.kid.system.model.resp.PuzzleUserProgressResp;
import com.redbook.kid.system.model.entity.PuzzleUserProgressDO;

import java.util.List;
import java.util.Map;

/**
 * 用户拼图关卡业务接口
 *
 * <AUTHOR>
 * @since 2025/04/18 16:28
 */
public interface PuzzleUserProgressService extends BaseService<PuzzleUserProgressResp, PuzzleUserProgressDetailResp, PuzzleUserProgressQuery, PuzzleUserProgressReq>,IService<PuzzleUserProgressDO> {
    
    /**
     * 更新用户关卡完成状态
     * 
     * @param userId 用户ID
     * @param levelNumber 关卡编号
     * @param completionTime 完成时间(秒)
     * @param isPerfect 是否完美完成
     * @return 是否更新成功
     */
    boolean updateLevelCompletion(String userId, Integer levelNumber, Integer completionTime, boolean isPerfect,boolean isCompleted);
    
    /**
     * 获取用户已完成的关卡列表
     * 
     * @param userId 用户ID
     * @return 已完成的关卡列表
     */
    List<PuzzleUserProgressDO> getCompletedLevels(String userId);
    
    /**
     * 获取用户关卡完成信息
     * 
     * @param userId 用户ID
     * @param levelNumber 关卡编号
     * @return 关卡完成信息
     */
    PuzzleUserProgressDO getLevelStatus(String userId, Integer levelNumber);
    
    /**
     * 获取用户最高已通过的关卡
     * 
     * @param userId 用户ID
     * @return 最高已通过关卡
     */
    Integer getHighestCompletedLevel(String userId);
    
    /**
     * 获取用户可进入的下一个关卡
     * 
     * @param userId 用户ID
     * @return 下一个可进入关卡
     */
    Integer getNextAvailableLevel(String userId);
    
    /**
     * 获取用户关卡完成进度
     * 
     * @param userId 用户ID
     * @return 完成进度(0.0-1.0)
     */
    double getCompletionProgress(String userId);
    
    /**
     * 判断用户是否可以解锁指定关卡
     * 
     * @param userId 用户ID
     * @param levelNumber 关卡编号
     * @return 是否可以解锁
     */
    boolean canUnlockLevel(String userId, Integer levelNumber);
    

    
    /**
     * 获取用户所有关卡进度概况
     * 
     * @param userId 用户ID
     * @return 所有关卡进度
     */
    Map<Integer, Map<String, Object>> getAllLevelProgress(String userId);
    
    /**
     * 获取关卡总数
     * 
     * @return 关卡总数
     */
    int getTotalLevelCount();
    
    /**
     * 获取用户连续完成的关卡数量
     *
     * @param userId 用户ID
     * @return 连续完成关卡数
     */
    int getConsecutiveCompletedLevels(String userId);

    /**
     * 获取用户已获得的连续通关奖励里程碑
     *
     * @param userId 用户ID
     * @return 已获得的最高里程碑数量，如果没有记录则返回0
     */
    int getUserConsecutiveRewardMilestone(String userId);

    /**
     * 更新用户连续通关奖励里程碑
     *
     * @param userId 用户ID
     * @param milestoneCount 里程碑数量
     * @return 是否更新成功
     */
    boolean updateConsecutiveRewardMilestone(String userId, int milestoneCount);
}