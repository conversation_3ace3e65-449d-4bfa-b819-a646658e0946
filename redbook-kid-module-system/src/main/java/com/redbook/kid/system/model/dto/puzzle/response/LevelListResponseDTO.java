package com.redbook.kid.system.model.dto.puzzle.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 获取关卡列表响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "获取关卡列表响应")
public class LevelListResponseDTO extends PuzzleBaseResponseDTO {

    /**
     * 关卡列表
     */
    @Schema(description = "关卡列表")
    private List<LevelInfoDTO> levels;
    
    /**
     * 游戏进度统计信息
     */
    @Schema(description = "游戏进度统计信息")
    private PuzzleProgressDTO progress;

    /**
     * 总关卡数
     * 注：方便前端使用，此字段与progress.totalLevels重复
     */
    @Schema(description = "总关卡数")
    private Integer totalLevels;
    
    /**
     * 已完成关卡数
     * 注：方便前端使用，此字段与progress.completedLevels重复
     */
    @Schema(description = "已完成关卡数")
    private Integer completedLevels;
    
    /**
     * 最高已解锁关卡
     * 注：方便前端使用，此字段与progress.highestUnlockedLevel重复
     */
    @Schema(description = "最高已解锁关卡")
    private Integer highestUnlockedLevel;

    @Schema(description = "每日可玩次数")
    private Integer daysPlayCount;

    /**
     * 今日剩余可玩次数
     */
    @Schema(description = "今日剩余可玩次数")
    private Integer playCountLeft;

    /**
     * 新获得且未弹窗的勋章列表
     */
    @Schema(description = "新获得且未弹窗的勋章列表")
    private List<AchievementDTO> newAchievementsToPop;

//    /**
//     * 完成进度(百分比)
//     * 注：方便前端使用，此字段与progress.completionProgress重复
//     */
//    @Schema(description = "完成进度(百分比)")
//    private Double completionProgress;
}
