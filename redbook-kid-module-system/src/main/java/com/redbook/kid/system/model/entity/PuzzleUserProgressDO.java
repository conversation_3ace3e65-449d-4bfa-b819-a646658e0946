package com.redbook.kid.system.model.entity;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseIdDO;

import java.io.Serial;
import java.time.*;

/**
 * 用户拼图关卡
 *
 * <AUTHOR>
 * @since 2025/04/18 16:28
 */
@Data
@TableName("lollipop_game_park.puzzle_user_progress")
@Schema(description = "用户拼图关卡")
public class PuzzleUserProgressDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户的唯一标识
     */
    @Schema(description = "用户的唯一标识")
    private String userId;

    /**
     * 关联的关卡
     */
    @Schema(description = "关联的关卡")
    private Integer levelNumber;

    /**
     * 标识用户是否完成该关卡
     */
    @Schema(description = "标识用户是否完成该关卡")
    private Boolean isCompleted;

    /** 用户最后一次游玩该关卡的时间 */
    @Schema(description = "用户最后一次游玩该关卡的时间")
    private LocalDateTime lastPlayed;

    /** 首次通关时间 */
    @Schema(description = "首次通关时间")
    private LocalDateTime firstCompletedTime;

    /** 首次是否完美通关 */
    @Schema(description = "首次是否完美通关")
    private Boolean firstPerfectCompletion;

    /** 最后一次通关时间 */
    @Schema(description = "最后一次通关时间")
    private LocalDateTime lastCompletedTime;

    /** 最后一次是否完美通关 */
    @Schema(description = "最后一次是否完美通关")
    private Boolean lastPerfectCompletion;

    /** 游玩次数 */
    @Schema(description = "游玩次数")
    private Integer playCount;

    /** 通关次数 */
    @Schema(description = "通关次数")
    private Integer passCount;

    /** 最优通关时间（秒） */
    @Schema(description = "最优通关时间（秒）")
    private Integer bestCompletionTime;

    /** 是否已获得限时突破奖励 */
    @Schema(description = "是否已获得限时突破奖励")
    private Boolean quickCompletionRewardReceived;

    /** 是否已获得连续通关奖励 */
    @Schema(description = "是否已获得连续通关奖励")
    private Boolean consecutiveCompletionRewardReceived;
}