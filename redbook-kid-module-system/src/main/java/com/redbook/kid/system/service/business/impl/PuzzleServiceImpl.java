package com.redbook.kid.system.service.business.impl;

import com.redbook.kid.common.constant.LollipopFromEnum;
import com.redbook.kid.system.config.PuzzleGameConfig;
import com.redbook.kid.system.config.RedisKeyConstant;
import com.redbook.kid.system.model.dto.puzzle.request.*;
import com.redbook.kid.system.model.dto.puzzle.response.*;
import com.redbook.kid.system.model.entity.PuzzleUserProgressDO;
import com.redbook.kid.system.model.entity.PuzzleUserWordDO;
import com.redbook.kid.system.model.entity.PuzzleWordsDO;
import com.redbook.kid.system.model.req.LollipopRecordReq;
import com.redbook.kid.system.service.LollipopRecordService;
import com.redbook.kid.system.service.business.PuzzleService;
import com.redbook.kid.system.service.puzzle.PuzzleUserAchievementsService;
import com.redbook.kid.system.service.puzzle.PuzzleUserProgressService;
import com.redbook.kid.system.service.puzzle.PuzzleUserWordService;
import com.redbook.kid.system.service.puzzle.PuzzleWordsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.starter.core.validation.ValidationUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 拼图游戏业务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PuzzleServiceImpl implements PuzzleService {
    
    private final PuzzleWordsService puzzleWordsService;
    private final PuzzleUserWordService puzzleUserWordService;
    private final PuzzleUserProgressService puzzleUserProgressService;
    private final PuzzleUserAchievementsService puzzleUserAchievementsService;
    private final LollipopRecordService lollipopRecordService;
    private final StringRedisTemplate stringRedisTemplate;
    private static final int MAX_PLAY_COUNT_PER_DAY = 3000;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 获取用户可用关卡列表
     */
    @Override
    public LevelListResponseDTO getLevelList(LevelListRequestDTO request) {
        String userId = request.getUserId();
        int left = getTodayPlayCountLeft(userId);
        LevelListResponseDTO responseDTO = new LevelListResponseDTO();
        responseDTO.setPlayCountLeft(left);
        responseDTO.setDaysPlayCount(MAX_PLAY_COUNT_PER_DAY);

        // 获取所有关卡信息
        List<LevelInfoDTO> levelInfoList = new ArrayList<>();
        int totalLevels = PuzzleGameConfig.TOTAL_LEVELS;
        
        // 获取用户已完成的关卡
        List<PuzzleUserProgressDO> completedLevels = puzzleUserProgressService.getCompletedLevels(userId);
        int completedCount = completedLevels.size();
        
        // 获取用户最高解锁关卡
        Integer highestCompletedLevel = puzzleUserProgressService.getHighestCompletedLevel(userId);
        
        // 获取用户所有关卡进度
        Map<Integer, Map<String, Object>> allProgress = puzzleUserProgressService.getAllLevelProgress(userId);
        
        // 构建关卡信息列表
        for (int i = 1; i <= totalLevels; i++) {
            LevelInfoDTO levelInfo = new LevelInfoDTO();
            levelInfo.setLevelNumber(i);
            levelInfo.setLevelName("第" + i + "关");
            
            // 设置关卡区域信息
            PuzzleGameConfig.ZoneConfig zoneConfig = PuzzleGameConfig.getZoneByLevel(i);
            levelInfo.setZoneId(zoneConfig.getId());
            levelInfo.setZoneName(zoneConfig.getName());
            levelInfo.setPiecesCount(zoneConfig.getPiecesCount());
            
            // 设置关卡配置信息
            PuzzleGameConfig.LevelConfig levelConfig = PuzzleGameConfig.getLevelConfig(i);
            levelInfo.setTotalTimeLimit(levelConfig.getTimeLimit());
            levelInfo.setCandyReward(levelConfig.getCandyReward());
            
            // 设置游戏机制
            levelInfo.setHasSilhouette(zoneConfig.isHasSilhouette());
            levelInfo.setHasFlashOriginal(zoneConfig.isHasFlashOriginal());
            
            // 设置用户进度信息
            Map<String, Object> progress = allProgress.get(i);

            
            if (progress != null) {
                // 设置进度数据
                levelInfo.setUnlocked(true);
                levelInfo.setCompleted(Boolean.TRUE.equals(progress.get("isCompleted")));
                // 新结构：只保留最优成绩、通关次数、最后游玩、最后是否完美
                levelInfo.setBestCompletionTime((Integer)progress.get("bestCompletionTime"));
                levelInfo.setPassCount((Integer)progress.get("passCount"));
                levelInfo.setPlayCount((Integer)progress.get("playCount"));
                levelInfo.setLastPerfectCompletion((Boolean)progress.get("lastPerfectCompletion"));
                // 日期
                if (progress.get("lastPlayed") instanceof LocalDateTime lastPlayed) {
                    Date convertedDate = Date.from(lastPlayed.atZone(ZoneId.systemDefault()).toInstant());
                    levelInfo.setLastPlayed(convertedDate);
//                    progressDTO.setLastPlayed(convertedDate);
                }
            } else {
                // 对于没有进度记录的关卡，第一关默认解锁，其他关卡需要前一关完成
                boolean unlocked = (i == 1) || (i <= highestCompletedLevel + 1);
                levelInfo.setUnlocked(unlocked);
                levelInfo.setCompleted(false);
                levelInfo.setPassCount(0);
                levelInfo.setPlayCount(0);
                levelInfo.setLastPerfectCompletion(false);
            }
            // 设置进度对象
//            levelInfo.setProgress(progressDTO);
            levelInfoList.add(levelInfo);
        }
        
        // 设置响应数据
        responseDTO.setLevels(levelInfoList);
        responseDTO.setTotalLevels(totalLevels);
        responseDTO.setCompletedLevels(completedCount);
        responseDTO.setHighestUnlockedLevel(highestCompletedLevel + 1);

        // 设置进度统计信息
        PuzzleProgressDTO progressStats = new PuzzleProgressDTO();
        progressStats.setTotalLevels(totalLevels);
        progressStats.setCompletedLevels(completedCount);
        progressStats.setHighestUnlockedLevel(highestCompletedLevel + 1);

        // 添加其他统计数据
        int totalWords = puzzleWordsService.getTotalWordsCount();
        int masteredWords = puzzleUserWordService.getMasteredWordIds(userId).size();
        progressStats.setTotalWordCount(totalWords);
        progressStats.setMasteredWordCount(masteredWords);
        
        int totalAchievements = puzzleUserAchievementsService.getAllAchievementConfigs().size();
        int unlockedAchievements = puzzleUserAchievementsService.countUnlockedAchievements(userId);
        progressStats.setTotalAchievementCount(totalAchievements);
        progressStats.setUnlockedAchievementCount(unlockedAchievements);
        
        responseDTO.setProgress(progressStats);
        
        // 查询新获得且未弹窗的勋章
        List<AchievementDTO> newAchievementsToPop = puzzleUserAchievementsService.getUnviewedAchievements(userId);
        if (newAchievementsToPop != null && !newAchievementsToPop.isEmpty()) {
            responseDTO.setNewAchievementsToPop(newAchievementsToPop);
        }

        return responseDTO;
    }
    
    /**
     * 获取关卡详情，包括单词信息
     */
    @Override
    public LevelDetailResponseDTO getLevelDetail(LevelDetailRequestDTO request) {
        String userId = request.getUserId();
        int left = getTodayPlayCountLeft(userId);
        ValidationUtils.throwIf(left<=0, "今日拼图游戏次数已用完，请明天再来");
        // 计数+1
        String today = java.time.LocalDate.now().format(DATE_FORMATTER);
        String key = RedisKeyConstant.getPuzzlePlayCountKey(userId, today);
        Long after = stringRedisTemplate.opsForValue().increment(key);
        if (after != null && after == 1) {
            stringRedisTemplate.expire(key, java.time.Duration.ofDays(1));
        }

        LevelDetailResponseDTO responseDTO = new LevelDetailResponseDTO();
        responseDTO.setLevelNumber(request.getLevelNumber());

        // 获取关卡单词列表
        List<PuzzleWordsDO> levelWords = puzzleWordsService.getWordsByLevel(request.getLevelNumber());
        List<WordDTO> wordDTOList = new ArrayList<>();
        // 获取用户已掌握的单词
        List<Integer> masteredWordIds = puzzleUserWordService.getMasteredWordIds(userId);
        for (PuzzleWordsDO word : levelWords) {
            WordDTO wordDTO = new WordDTO();
            wordDTO.setId(word.getId().intValue());
            wordDTO.setWord(word.getWord());
            wordDTO.setSpell(word.getSpell());
            wordDTO.setMeaning(word.getMeaning());
            wordDTO.setSoundPath(word.getSoundPath());
            wordDTO.setImagePath(word.getImagePath());
            wordDTO.setLevelNumber(word.getLevelNumber());
            wordDTO.setMastered(masteredWordIds.contains(word.getId().intValue()));
            wordDTOList.add(wordDTO);
        }
        responseDTO.setWords(wordDTOList);

        // 获取用户关卡进度
        PuzzleUserProgressDO progress = puzzleUserProgressService.getLevelStatus(userId, request.getLevelNumber());
        LevelProgressDTO progressDTO = new LevelProgressDTO();
        if (progress != null) {
            progressDTO.setIsCompleted(progress.getIsCompleted());
            progressDTO.setPassCount(progress.getPassCount());
            progressDTO.setPlayCount(progress.getPlayCount());
            progressDTO.setBestCompletionTime(progress.getBestCompletionTime());
            progressDTO.setLastPerfectCompletion(progress.getLastPerfectCompletion());
            if (progress.getLastPlayed() != null) {
                progressDTO.setLastPlayed(java.util.Date.from(progress.getLastPlayed().atZone(java.time.ZoneId.systemDefault()).toInstant()));
            }
        } else {
            progressDTO.setIsCompleted(false);
            progressDTO.setPassCount(0);
            progressDTO.setPlayCount(0);
            progressDTO.setBestCompletionTime(null);
            progressDTO.setLastPerfectCompletion(false);
        }
        responseDTO.setProgress(progressDTO);

        // 检查用户是否可以玩此关卡
        boolean canPlay = puzzleUserProgressService.canUnlockLevel(userId, request.getLevelNumber());
        responseDTO.setCanPlay(canPlay);
        return responseDTO;
    }
    
    /**
     * 提交关卡完成结果
     */
    @Override
    @Transactional
    public LevelResultResponseDTO submitLevelResult(LevelResultRequestDTO request) {
        String userId = request.getUserId();
        Integer levelNumber = request.getLevelNumber();
        Integer completionTime = request.getCompletionTime();
        boolean isPerfect = request.getIsPerfect();
        Boolean isCompleted = request.getIsCompleted();
        List<Integer> completedWordIds = request.getCompletedWordIds();
        
        LevelResultResponseDTO responseDTO = new LevelResultResponseDTO();
        responseDTO.setLevelNumber(levelNumber);
        responseDTO.setCompletionTime(completionTime);
        responseDTO.setIsPerfect(isPerfect);
        responseDTO.setIsCompleted(request.getIsCompleted());
        // 更新用户关卡完成状态
        boolean updated = puzzleUserProgressService.updateLevelCompletion(
                userId, levelNumber, completionTime, isPerfect,isCompleted);
        responseDTO.setSuccess(updated);

        // 更新用户单词掌握状态
        if (completedWordIds != null && !completedWordIds.isEmpty()) {
            for (Integer wordId : completedWordIds) {
                puzzleUserWordService.updateWordMasteryStatus(userId, wordId, true);
            }
            // 获取已掌握的单词
            List<Integer> masteredWordIds = puzzleUserWordService.getMasteredWordIds(userId);
            responseDTO.setMasteredWordCount(masteredWordIds.size());
        }

        if (!isCompleted){
            // 如果未完成，直接返回
            return responseDTO;
        }

        // 检查并解锁成就
        List<String> newAchievements = puzzleUserAchievementsService.checkAndUnlockAchievements(userId);
//        responseDTO.setNewAchievements(newAchievements);
        
        // 计算基础奖励糖果
        int candyReward = calculateCandyReward(levelNumber, isPerfect, completionTime, userId);

        // 检查连续通关奖励
        int consecutiveReward = checkConsecutiveCompletionReward(userId, levelNumber);
        if (consecutiveReward > 0) {
            candyReward += consecutiveReward;
            log.info("用户{}获得连续通关奖励{}个棒棒糖", userId, consecutiveReward);
        }
        
        responseDTO.setCandyReward(candyReward);
        
        // 添加糖果记录
        lollipopRecordService.create(LollipopRecordReq.builder()
                .userId(userId)
                .lollipop(candyReward)
                .module(LollipopFromEnum.PUZZLE_MODULE)
                .build());
        
        // 获取下一关信息
        Integer nextLevel = puzzleUserProgressService.getNextAvailableLevel(userId);
        responseDTO.setNextLevel(nextLevel);
        
        return responseDTO;
    }
    
    /**
     * 获取用户已掌握的单词列表
     */
    @Override
    public MasteredWordsResponseDTO getMasteredWords(MasteredWordsRequestDTO request) {
        String userId = request.getUserId();
        
        MasteredWordsResponseDTO responseDTO = new MasteredWordsResponseDTO();
        
        // 获取用户已掌握的单词信息
        List<PuzzleUserWordDO> userWords = puzzleUserWordService.getMasteredWords(userId);
        
        // 获取单词详情
        List<MasteredWordDTO> masteredWords = new ArrayList<>();
        for (PuzzleUserWordDO userWord : userWords) {
            PuzzleWordsDO word = puzzleWordsService.getWordById(userWord.getWordId().longValue());
            if (word != null) {
                // 创建WordDTO
                WordDTO wordDTO = new WordDTO();
                wordDTO.setId(userWord.getWordId());
                wordDTO.setWord(word.getWord());
                wordDTO.setSpell(word.getSpell());
                wordDTO.setMeaning(word.getMeaning());
                wordDTO.setImagePath(word.getImagePath());
                wordDTO.setSoundPath(word.getSoundPath());
                wordDTO.setLevelNumber(word.getLevelNumber());
                wordDTO.setMastered(true);
                
                // 创建MasteredWordDTO并设置属性
                MasteredWordDTO masteredWordDTO = new MasteredWordDTO();
                masteredWordDTO.setWord(wordDTO);
                masteredWordDTO.setIsMastered(true);
                
                // 从PuzzleUserWordDO获取最后练习时间并转换为Date
                if (userWord.getLastPracticed() != null) {
                    Date lastPracticed = Date.from(userWord.getLastPracticed().atZone(ZoneId.systemDefault()).toInstant());
                    masteredWordDTO.setLastPracticed(lastPracticed);
                }
                
                masteredWords.add(masteredWordDTO);
            }
        }
        
        responseDTO.setWords(masteredWords);
        responseDTO.setMasteredCount(masteredWords.size());
        responseDTO.setTotalCount(masteredWords.size());
        
        return responseDTO;
    }
    
    /**
     * 获取用户已获得的成就列表
     */
    @Override
    public AchievementsResponseDTO getUserAchievements(AchievementsRequestDTO request) {
        String userId = request.getUserId();
        
        AchievementsResponseDTO responseDTO = new AchievementsResponseDTO();
        
        // 获取用户已解锁的成就
        List<String> unlockedAchievements = puzzleUserAchievementsService.getUnlockedAchievementCodes(userId);
        
        // 获取所有成就配置
        List<AchievementDTO> allAchievements = puzzleUserAchievementsService.getAllAchievementConfigs();
        
        // 标记已解锁的成就
        for (AchievementDTO achievement : allAchievements) {
            achievement.setUnlocked(unlockedAchievements.contains(achievement.getCode()));
        }
        
        responseDTO.setAchievements(allAchievements);
        responseDTO.setUnlockedCount(unlockedAchievements.size());
        responseDTO.setTotalCount(allAchievements.size());
        
        return responseDTO;
    }
    
    /**
     * 计算关卡奖励的糖果数量
     */
    private int calculateCandyReward(int levelNumber, boolean isPerfect, Integer completionTime, String userId) {
        PuzzleGameConfig.LevelConfig levelConfig = PuzzleGameConfig.getLevelConfig(levelNumber);
        int baseReward = levelConfig.getCandyReward();
        int totalReward = baseReward;

        // 限时突破奖励：在时间限制的一半内完成，且该关卡未获得过限时突破奖励
        if (completionTime != null && completionTime <= levelConfig.getTimeLimit() / 2) {
            // 检查该关卡是否已经获得过限时突破奖励
            PuzzleUserProgressDO progress = puzzleUserProgressService.getLevelStatus(userId, levelNumber);
            if (progress == null || !Boolean.TRUE.equals(progress.getQuickCompletionRewardReceived())) {
                totalReward += PuzzleGameConfig.QUICK_COMPLETION_REWARD;
                // 标记该关卡已获得限时突破奖励
                markQuickCompletionRewardReceived(userId, levelNumber);
                log.info("用户{}在关卡{}获得限时突破奖励{}个棒棒糖", userId, levelNumber, PuzzleGameConfig.QUICK_COMPLETION_REWARD);
            } else {
                log.debug("用户{}在关卡{}已获得过限时突破奖励，跳过发放", userId, levelNumber);
            }
        }

        return totalReward;
    }
    
    /**
     * 检查是否获得连续通关奖励
     *
     * @param userId 用户ID
     * @param currentLevelNumber 当前完成的关卡编号
     * @return 额外奖励棒棒糖数
     */
    private int checkConsecutiveCompletionReward(String userId, Integer currentLevelNumber) {
        // 获取用户总完成关卡数
        int totalCompletedLevels = puzzleUserProgressService.getCompletedLevels(userId).size();

        // 获取用户已获得奖励的里程碑
        int currentMilestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(userId);

        // 计算有效连续数（排除已奖励的关卡）
        int effectiveConsecutiveCount = totalCompletedLevels - currentMilestone;

        // 检查是否满足奖励条件
        if (effectiveConsecutiveCount >= 3 && effectiveConsecutiveCount % 3 == 0) {
            // 检查最后一次是否完美通关
            PuzzleUserProgressDO currentProgress = puzzleUserProgressService.getLevelStatus(userId, currentLevelNumber);
            if (currentProgress != null && Boolean.TRUE.equals(currentProgress.getLastPerfectCompletion())) {
                // 发放奖励并更新里程碑
                boolean updated = puzzleUserProgressService.updateUserConsecutiveRewardMilestone(userId, totalCompletedLevels);
                if (updated) {
                    log.info("用户{}达成连续通关{}关里程碑，获得连续通关奖励{}个棒棒糖",
                            userId, totalCompletedLevels, PuzzleGameConfig.CONSECUTIVE_COMPLETION_REWARD);
                    return PuzzleGameConfig.CONSECUTIVE_COMPLETION_REWARD;
                } else {
                    log.error("用户{}更新连续通关里程碑失败", userId);
                }
            } else {
                log.debug("用户{}最后一次通关不是完美通关，跳过连续通关奖励发放", userId);
            }
        }

        return 0;
    }

    @Override
    public int getTodayPlayCountLeft(String userId) {
        String today = java.time.LocalDate.now().format(DATE_FORMATTER);
        String key = RedisKeyConstant.getPuzzlePlayCountKey(userId, today);
        String countStr = stringRedisTemplate.opsForValue().get(key);
        int count = countStr == null ? 0 : Integer.parseInt(countStr);
        return Math.max(0, MAX_PLAY_COUNT_PER_DAY - count);
    }

    /**
     * 标记用户在指定关卡已获得限时突破奖励
     *
     * @param userId 用户ID
     * @param levelNumber 关卡编号
     */
    private void markQuickCompletionRewardReceived(String userId, Integer levelNumber) {
        try {
            PuzzleUserProgressDO progress = puzzleUserProgressService.getLevelStatus(userId, levelNumber);
            if (progress != null) {
                progress.setQuickCompletionRewardReceived(true);
                puzzleUserProgressService.updateById(progress);
            }
        } catch (Exception e) {
            log.error("标记用户{}关卡{}限时突破奖励状态失败", userId, levelNumber, e);
        }
    }

    /**
     * 标记用户在指定关卡已获得连续通关奖励
     *
     * @param userId 用户ID
     * @param levelNumber 关卡编号
     */
    private void markConsecutiveCompletionRewardReceived(String userId, Integer levelNumber) {
        try {
            PuzzleUserProgressDO progress = puzzleUserProgressService.getLevelStatus(userId, levelNumber);
            if (progress != null) {
                progress.setConsecutiveCompletionRewardReceived(true);
                puzzleUserProgressService.updateById(progress);
            }
        } catch (Exception e) {
            log.error("标记用户{}关卡{}连续通关奖励状态失败", userId, levelNumber, e);
        }
    }


    @Override
    public void markAchievementsAsViewed(String userId, String achievementCode) {
        puzzleUserAchievementsService.markAchievementsAsViewed(userId, Collections.singletonList(achievementCode));
    }
}
