package com.redbook.kid.system.service.puzzle.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.redbook.kid.system.config.PuzzleGameConfig;
import com.redbook.kid.system.mapper.PuzzleUserProgressMapper;
import com.redbook.kid.system.model.entity.PuzzleUserProgressDO;
import com.redbook.kid.system.model.query.PuzzleUserProgressQuery;
import com.redbook.kid.system.model.req.PuzzleUserProgressReq;
import com.redbook.kid.system.model.resp.PuzzleUserProgressDetailResp;
import com.redbook.kid.system.model.resp.PuzzleUserProgressResp;
import com.redbook.kid.system.service.puzzle.PuzzleUserProgressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户拼图关卡业务实现
 *
 * <AUTHOR>
 * @since 2025/04/18 16:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PuzzleUserProgressServiceImpl extends BaseServiceImpl<PuzzleUserProgressMapper, PuzzleUserProgressDO, PuzzleUserProgressResp, PuzzleUserProgressDetailResp, PuzzleUserProgressQuery, PuzzleUserProgressReq> implements PuzzleUserProgressService {

    @Autowired
    private PuzzleUserProgressMapper puzzleUserProgressMapper;

    /**
     * 更新用户关卡完成状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLevelCompletion(String userId, Integer levelNumber, Integer completionTime, boolean isPerfect, boolean isCompleted) {
        if (levelNumber < 1 || levelNumber > PuzzleGameConfig.TOTAL_LEVELS) {
            log.error("关卡编号无效: {}", levelNumber);
            return false;
        }

        // 查询用户关卡进度
        PuzzleUserProgressDO progress = this.getLevelStatus(userId, levelNumber);
        LocalDateTime now = LocalDateTime.now();

        if (progress == null) {
            progress = new PuzzleUserProgressDO();
            progress.setUserId(userId);
            progress.setLevelNumber(levelNumber);
            progress.setIsCompleted(isCompleted);
            progress.setLastPlayed(now);
            progress.setPlayCount(1);
            progress.setPassCount(isCompleted ? 1 : 0);
            // 初始化奖励状态
            progress.setQuickCompletionRewardReceived(false);
            progress.setConsecutiveRewardMilestone(0);
            if (isCompleted) {
                progress.setFirstCompletedTime(now);
                progress.setFirstPerfectCompletion(isPerfect);
                progress.setLastCompletedTime(now);
                progress.setLastPerfectCompletion(isPerfect);
                progress.setBestCompletionTime(completionTime);
            }
            return this.save(progress);
        }

        // 已有进度记录
        // 游玩次数+1
        progress.setPlayCount(progress.getPlayCount() == null ? 1 : progress.getPlayCount() + 1);
        progress.setLastPlayed(now);

        if (isCompleted) {
            // 通关次数+1
            progress.setPassCount(progress.getPassCount() == null ? 1 : progress.getPassCount() + 1);
            // 首次通关
            if (progress.getFirstCompletedTime() == null) {
                progress.setFirstCompletedTime(now);
                progress.setFirstPerfectCompletion(isPerfect);
            }
            // 最后一次通关
            progress.setLastCompletedTime(now);
            progress.setLastPerfectCompletion(isPerfect);
            // 最优成绩
            if (progress.getBestCompletionTime() == null || completionTime < progress.getBestCompletionTime()) {
                progress.setBestCompletionTime(completionTime);
            }
            // 兼容老字段 isCompleted
            if (!Boolean.TRUE.equals(progress.getIsCompleted())) {
                progress.setIsCompleted(true);
            }
        }

        boolean updated = this.updateById(progress);

        // 如果更新成功且当前关卡不是最后一关，自动解锁下一关
        if (isCompleted && updated && levelNumber < PuzzleGameConfig.TOTAL_LEVELS) {
            this.unlockNextLevel(userId, levelNumber + 1);
        }

        return updated;
    }

    /**
     * 获取用户已完成的关卡列表
     */
    @Override
    public List<PuzzleUserProgressDO> getCompletedLevels(String userId) {
        return this.list(
                new LambdaQueryWrapper<PuzzleUserProgressDO>()
                        .eq(PuzzleUserProgressDO::getUserId, userId)
                        .eq(PuzzleUserProgressDO::getIsCompleted, true)
                        .orderByAsc(PuzzleUserProgressDO::getLevelNumber)
        );
    }

    /**
     * 获取用户关卡完成信息
     */
    @Override
    public PuzzleUserProgressDO getLevelStatus(String userId, Integer levelNumber) {
        return this.getOne(
                new LambdaQueryWrapper<PuzzleUserProgressDO>()
                        .eq(PuzzleUserProgressDO::getUserId, userId)
                        .eq(PuzzleUserProgressDO::getLevelNumber, levelNumber)
        );
    }

    /**
     * 获取用户最高已通过的关卡
     */
    @Override
    public Integer getHighestCompletedLevel(String userId) {
        List<PuzzleUserProgressDO> completedLevels = getCompletedLevels(userId);
        if (completedLevels.isEmpty()) {
            return 0; // 没有完成任何关卡
        }
        
        return completedLevels.stream()
                .mapToInt(PuzzleUserProgressDO::getLevelNumber)
                .max()
                .orElse(0);
    }

    /**
     * 获取用户可进入的下一个关卡
     */
    @Override
    public Integer getNextAvailableLevel(String userId) {
        Integer highestLevel = getHighestCompletedLevel(userId);
        // 如果已完成最后一关，返回最后一关
        if (highestLevel >= PuzzleGameConfig.TOTAL_LEVELS) {
            return PuzzleGameConfig.TOTAL_LEVELS;
        }
        // 否则返回下一关
        return highestLevel + 1;
    }

    /**
     * 获取用户关卡完成进度
     */
    @Override
    public double getCompletionProgress(String userId) {
        int completedCount = getCompletedLevels(userId).size();
        return (double) completedCount / PuzzleGameConfig.TOTAL_LEVELS;
    }

    /**
     * 判断用户是否可以解锁指定关卡
     */
    @Override
    public boolean canUnlockLevel(String userId, Integer levelNumber) {
        // 第一关默认解锁
        if (levelNumber == 1) {
            return true;
        }
        
        // 其他关卡需要完成前一关
        PuzzleUserProgressDO previousLevel = getLevelStatus(userId, levelNumber - 1);
        return previousLevel != null && previousLevel.getIsCompleted();
    }


    /**
     * 解锁下一关卡
     */
    private boolean unlockNextLevel(String userId, Integer nextLevelNumber) {
        PuzzleUserProgressDO nextLevel = getLevelStatus(userId, nextLevelNumber);
        if (nextLevel != null) {
            return true;
        }
        nextLevel = new PuzzleUserProgressDO();
        nextLevel.setUserId(userId);
        nextLevel.setLevelNumber(nextLevelNumber);
        nextLevel.setIsCompleted(false);
        nextLevel.setPlayCount(0);
        nextLevel.setPassCount(0);
        nextLevel.setLastPlayed(LocalDateTime.now());
        // 初始化奖励状态
        nextLevel.setQuickCompletionRewardReceived(false);
        nextLevel.setConsecutiveCompletionRewardReceived(false);
        return this.save(nextLevel);
    }

    /**
     * 获取用户所有关卡进度概况
     */
    @Override
    public Map<Integer, Map<String, Object>> getAllLevelProgress(String userId) {
        List<PuzzleUserProgressDO> allProgress = this.list(
                new LambdaQueryWrapper<PuzzleUserProgressDO>()
                        .eq(PuzzleUserProgressDO::getUserId, userId)
        );
        
        Map<Integer, Map<String, Object>> result = new HashMap<>();
        
        for (PuzzleUserProgressDO progress : allProgress) {
            Map<String, Object> levelInfo = new HashMap<>();
            levelInfo.put("levelNumber", progress.getLevelNumber());
            levelInfo.put("isCompleted", progress.getIsCompleted());
            levelInfo.put("passCount", progress.getPassCount());
            levelInfo.put("playCount", progress.getPlayCount());
            levelInfo.put("bestCompletionTime", progress.getBestCompletionTime());
            levelInfo.put("lastPerfectCompletion", progress.getLastPerfectCompletion());
            levelInfo.put("lastPlayed", progress.getLastPlayed());
            
            result.put(progress.getLevelNumber(), levelInfo);
        }
        
        return result;
    }

    /**
     * 获取关卡总数
     */
    @Override
    public int getTotalLevelCount() {
        return PuzzleGameConfig.TOTAL_LEVELS;
    }
    
    /**
     * 获取用户连续完成的关卡数量
     */
    @Override
    public int getConsecutiveCompletedLevels(String userId) {
        // 获取用户所有已完成的关卡，按关卡编号排序
        List<PuzzleUserProgressDO> completedLevels = getCompletedLevels(userId);
        if (completedLevels.isEmpty()) {
            return 0;
        }
        
        // 获取用户最近完成的关卡
        PuzzleUserProgressDO lastCompleted = completedLevels.stream()
                .max(Comparator.comparing(PuzzleUserProgressDO::getLastPlayed))
                .orElse(null);

        int lastLevelNumber = lastCompleted.getLevelNumber();
        int consecutiveCount = 1; // 至少完成了一关
        
        // 从最近完成的关卡往前检查，直到发现未完成的关卡或到达第一关
        for (int i = lastLevelNumber - 1; i >= 1; i--) {
            PuzzleUserProgressDO progress = getLevelStatus(userId, i);
            if (progress == null || !progress.getIsCompleted()) {
                break;
            }
            consecutiveCount++;
        }
        
        return consecutiveCount;
    }
}

