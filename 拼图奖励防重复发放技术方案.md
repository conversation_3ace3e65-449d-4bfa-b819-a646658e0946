# 拼图奖励防重复发放技术方案

## 1. 问题描述

### 当前问题
- 在`PuzzleServiceImpl.submitLevelResult`方法中，限时突破奖励和连续通关奖励会被多次发放给同一个用户
- 用户可以通过重复提交或重复游玩同一关卡获得本应只能获得一次的奖励

### 业务需求
- **限时突破奖励**：每个关卡中，每个用户只能获得一次限时突破奖励
- **连续通关奖励**：用户连续完成3关且每关都是完美通关时，获得额外20个棒棒糖奖励，每个里程碑只能获得一次
- 连续通关的定义：按用户游戏时间顺序连续完成关卡，不是按关卡编号顺序
- 防止用户通过重复提交或其他方式多次获得同样的奖励

## 2. 技术方案

### 2.1 数据库设计

在现有的`puzzle_user_progress`表中添加两个字段来跟踪奖励状态：

```sql
ALTER TABLE lollipop_game_park.puzzle_user_progress
ADD COLUMN quick_completion_reward_received BOOLEAN DEFAULT FALSE COMMENT '是否已获得限时突破奖励',
ADD COLUMN consecutive_reward_milestone INTEGER DEFAULT 0 COMMENT '连续通关奖励里程碑 - 记录获得奖励时的累计完成关卡数';
```

**字段说明：**
- `quick_completion_reward_received`: 关卡级别的限时突破奖励标记
- `consecutive_reward_milestone`: 用户级别的连续通关奖励里程碑，记录用户获得奖励时的累计完成关卡数

### 2.2 实体类修改

在`PuzzleUserProgressDO`中添加对应字段：

```java
/** 是否已获得限时突破奖励 */
@Schema(description = "是否已获得限时突破奖励")
private Boolean quickCompletionRewardReceived;

/** 连续通关奖励里程碑 - 记录获得奖励时的累计完成关卡数 */
@Schema(description = "连续通关奖励里程碑 - 记录获得奖励时的累计完成关卡数")
private Integer consecutiveRewardMilestone;
```

### 2.3 业务逻辑修改

#### 限时突破奖励防重复逻辑
1. 在`calculateCandyReward`方法中，检查用户是否满足限时突破条件
2. 如果满足条件，查询该关卡的`quick_completion_reward_received`字段
3. 如果为`false`或`null`，发放奖励并将字段设置为`true`
4. 如果为`true`，跳过奖励发放

#### 连续通关奖励防重复逻辑
1. 在`checkConsecutiveCompletionReward`方法中，计算用户总完成关卡数
2. 获取用户当前的连续通关奖励里程碑
3. 计算有效连续数：总完成数 - 当前里程碑
4. 如果有效连续数满足条件（≥3且%3==0），检查最近3关是否都是完美通关
5. 如果最近3关都是完美通关，发放奖励并更新里程碑为当前总完成数

## 3. 核心代码实现

### 3.1 修改后的calculateCandyReward方法

```java
private int calculateCandyReward(int levelNumber, boolean isPerfect, Integer completionTime, String userId) {
    PuzzleGameConfig.LevelConfig levelConfig = PuzzleGameConfig.getLevelConfig(levelNumber);
    int baseReward = levelConfig.getCandyReward();
    int totalReward = baseReward;

    // 限时突破奖励：在时间限制的一半内完成，且该关卡未获得过限时突破奖励
    if (completionTime != null && completionTime <= levelConfig.getTimeLimit() / 2) {
        PuzzleUserProgressDO progress = puzzleUserProgressService.getLevelStatus(userId, levelNumber);
        if (progress == null || !Boolean.TRUE.equals(progress.getQuickCompletionRewardReceived())) {
            totalReward += PuzzleGameConfig.QUICK_COMPLETION_REWARD;
            markQuickCompletionRewardReceived(userId, levelNumber);
            log.info("用户{}在关卡{}获得限时突破奖励{}个棒棒糖", userId, levelNumber, PuzzleGameConfig.QUICK_COMPLETION_REWARD);
        }
    }
    
    return totalReward;
}
```

### 3.2 修改后的checkConsecutiveCompletionReward方法

```java
private int checkConsecutiveCompletionReward(String userId, Integer currentLevelNumber) {
    // 获取用户总完成关卡数
    int totalCompletedLevels = puzzleUserProgressService.getCompletedLevels(userId).size();

    // 获取用户已获得奖励的里程碑
    int currentMilestone = puzzleUserProgressService.getUserConsecutiveRewardMilestone(userId);

    // 计算有效连续数（排除已奖励的关卡）
    int effectiveConsecutiveCount = totalCompletedLevels - currentMilestone;

    // 检查是否满足奖励条件
    if (effectiveConsecutiveCount >= 3 && effectiveConsecutiveCount % 3 == 0) {
        // 检查最近完成的3关是否都是完美通关
        if (areLastNLevelsPerfect(userId, 3)) {
            // 发放奖励并更新里程碑
            boolean updated = puzzleUserProgressService.updateUserConsecutiveRewardMilestone(userId, totalCompletedLevels);
            if (updated) {
                return PuzzleGameConfig.CONSECUTIVE_COMPLETION_REWARD;
            }
        }
    }

    return 0;
}
```

## 4. 技术要点

### 4.1 数据一致性和并发安全

1. **数据库约束**：使用`BOOLEAN DEFAULT FALSE`确保字段有明确的初始值
2. **事务保护**：`submitLevelResult`方法已使用`@Transactional`注解
3. **原子操作**：奖励发放和状态更新在同一个事务中完成
4. **幂等性**：重复调用不会产生副作用

### 4.2 性能影响最小化

1. **索引优化**：为新字段创建合适的索引
2. **查询优化**：复用现有的`getLevelStatus`查询，避免额外的数据库访问
3. **缓存友好**：新字段不影响现有的缓存策略

### 4.3 向后兼容性

1. **字段默认值**：新字段使用`DEFAULT FALSE`，对现有数据无影响
2. **空值处理**：代码中使用`Boolean.TRUE.equals()`安全处理null值
3. **渐进式部署**：可以先部署代码，再执行数据库迁移

### 4.4 错误处理机制

1. **异常捕获**：标记奖励状态的方法使用try-catch包装
2. **日志记录**：详细记录奖励发放和错误信息
3. **降级策略**：即使标记状态失败，也不影响基础游戏功能

## 5. 部署步骤

### 5.1 测试环境验证
1. 执行数据库迁移脚本
2. 部署修改后的代码
3. 运行单元测试和集成测试
4. 验证奖励防重复逻辑

### 5.2 生产环境部署
1. 在维护窗口执行数据库迁移
2. 部署新版本代码
3. 监控系统日志和奖励发放情况
4. 验证功能正常运行

## 6. 监控和验证

### 6.1 监控指标
- 奖励发放次数统计
- 重复奖励尝试次数
- 数据库更新操作耗时
- 异常错误率

### 6.2 验证方法
- 单元测试覆盖各种场景
- 集成测试验证端到端流程
- 生产环境数据分析
- 用户反馈收集

## 7. 风险评估

### 7.1 技术风险
- **低风险**：修改范围有限，主要是添加字段和检查逻辑
- **数据迁移风险**：新字段有默认值，迁移安全
- **性能风险**：影响极小，主要是增加了字段查询

### 7.2 业务风险
- **用户体验**：防止重复奖励，提升公平性
- **经济影响**：减少不当奖励发放，保护游戏经济平衡
- **兼容性**：向后兼容，不影响现有功能

## 8. 后续优化建议

1. **数据分析**：定期分析奖励发放数据，优化奖励机制
2. **性能优化**：如果查询量大，可考虑添加Redis缓存
3. **功能扩展**：可以扩展到其他类型的奖励防重复
4. **监控完善**：添加更详细的业务监控和告警
