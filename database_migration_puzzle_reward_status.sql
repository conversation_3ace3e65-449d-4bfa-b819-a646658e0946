-- 数据库迁移脚本：为puzzle_user_progress表添加奖励状态字段
-- 执行时间：2025-06-24

-- 1. 添加限时突破奖励状态字段
ALTER TABLE lollipop_game_park.puzzle_user_progress
ADD COLUMN quick_completion_reward_received BOOLEAN DEFAULT FALSE COMMENT '是否已获得限时突破奖励';

-- 2. 添加连续通关奖励里程碑字段
ALTER TABLE lollipop_game_park.puzzle_user_progress
ADD COLUMN consecutive_reward_milestone INTEGER DEFAULT 0 COMMENT '连续通关奖励里程碑 - 记录获得奖励时的累计完成关卡数';

-- 3. 为新字段创建索引以提高查询性能
CREATE INDEX idx_puzzle_progress_quick_reward ON lollipop_game_park.puzzle_user_progress (user_id, quick_completion_reward_received);
CREATE INDEX idx_puzzle_progress_consecutive_milestone ON lollipop_game_park.puzzle_user_progress (user_id, consecutive_reward_milestone);

-- 4. 更新现有数据：将所有现有记录的奖励状态初始化
-- 注意：由于我们无法确定历史数据中哪些用户已经获得过奖励，
-- 为了避免重复发放，建议将现有已完成关卡的奖励状态设置为已获得
-- 这样可以确保只有新完成的关卡才能获得奖励

-- 可选：如果希望对历史数据也进行奖励发放，可以保持默认值
-- 如果希望避免对历史数据重复发放奖励，可以执行以下语句：

-- 对于限时突破奖励，将所有已完成关卡标记为已获得
-- UPDATE lollipop_game_park.puzzle_user_progress
-- SET quick_completion_reward_received = TRUE
-- WHERE is_completed = TRUE;

-- 对于连续通关奖励，将每个用户的里程碑设置为当前已完成关卡数
-- UPDATE lollipop_game_park.puzzle_user_progress p1
-- SET consecutive_reward_milestone = (
--     SELECT COUNT(*)
--     FROM lollipop_game_park.puzzle_user_progress p2
--     WHERE p2.user_id = p1.user_id AND p2.is_completed = TRUE
-- )
-- WHERE p1.is_completed = TRUE;

-- 建议：在生产环境执行前，先在测试环境验证迁移脚本的正确性
