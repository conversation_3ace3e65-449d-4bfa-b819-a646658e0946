package com.redbook.kid.common.config.exception;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.redbook.kid.common.context.UserContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import top.continew.starter.core.exception.BadRequestException;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.web.model.R;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 统一处理系统异常，返回友好的错误信息
 *
 * <AUTHOR>
 * @since 2024/8/7 20:21
 */
@Slf4j
@Order(2)
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger errorLogger = LoggerFactory.getLogger("kid-err");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS");

    /**
     * 业务异常处理
     * 处理业务逻辑异常，返回500状态码
     */
    @ExceptionHandler(BusinessException.class)
    public R handleBusinessException(BusinessException e, HttpServletRequest request) {
//        logError(request, e);
        return R.fail(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), e.getMessage());
    }

    /**
     * 自定义验证异常处理
     * 处理参数校验异常，返回400状态码
     */
    @ExceptionHandler(BadRequestException.class)
    public R handleBadRequestException(BadRequestException e, HttpServletRequest request) {
//        logError(request, e);
        return R.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), e.getMessage());
    }

    /**
     * 参数绑定异常处理
     * 处理@Valid注解校验失败的情况
     */
    @ExceptionHandler(BindException.class)
    public R handleBindException(BindException e, HttpServletRequest request) {
        logError(request, e);
        String message = e.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        return R.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), message);
    }

    /**
     * 参数校验异常处理
     * 处理@Validated注解校验失败的情况
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public R handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        logError(request, e);
        String message = e.getConstraintViolations().iterator().next().getMessage();
        return R.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), message);
    }

    /**
     * 方法参数缺失异常处理
     * 处理@RequestParam参数缺失的情况
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public R handleMethodArgumentTypeMismatchException(MissingServletRequestParameterException e,
                                                       HttpServletRequest request) {
        logError(request, e);
        return R.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), "参数 '%s' 缺失".formatted(e.getParameterName()));
    }

    /**
     * 方法参数类型不匹配异常处理
     * 处理@RequestParam参数类型不匹配的情况
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public R handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e,
                                                       HttpServletRequest request) {
        logError(request, e);
        return R.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), "参数 '%s' 类型不匹配".formatted(e.getName()));
    }

    /**
     * HTTP消息不可读异常处理
     * 处理请求体解析异常的情况
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public R handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        logError(request, e);
        if (e.getCause() instanceof InvalidFormatException invalidFormatException) {
            return R.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), "参数 '%s' 类型不匹配"
                    .formatted(invalidFormatException.getValue()));
        }
        return R.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), "参数缺失或格式不正确");
    }

    /**
     * 文件上传异常处理
     * 处理文件上传大小超限等异常
     */
    @ExceptionHandler(MultipartException.class)
    public R handleMultipartException(MultipartException e, HttpServletRequest request) {
        logError(request, e);
        String msg = e.getMessage();
        R defaultFail = R.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), msg);
        if (CharSequenceUtil.isBlank(msg)) {
            return defaultFail;
        }
        String sizeLimit;
        Throwable cause = e.getCause();
        if (null != cause) {
            msg = msg.concat(cause.getMessage().toLowerCase());
        }
        if (msg.contains("larger than")) {
            sizeLimit = CharSequenceUtil.subAfter(msg, "larger than ", true);
        } else if (msg.contains("size") && msg.contains("exceed")) {
            sizeLimit = CharSequenceUtil.subBetween(msg, "the maximum size ", " for");
        } else {
            return defaultFail;
        }
        return R.fail(String.valueOf(HttpStatus.BAD_REQUEST.value()), "请上传小于 %s 的文件".formatted(FileUtil
                .readableFileSize(Long.parseLong(sizeLimit))));
    }

    /**
     * 404异常处理
     * 处理请求URL不存在的情况
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public R handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
//        logError(request, e);
        return R.fail(String.valueOf(HttpStatus.NOT_FOUND.value()), "请求 URL '%s' 不存在".formatted(request
                .getRequestURI()));
    }

    /**
     * HTTP方法不支持异常处理
     * 处理不支持的HTTP请求方法
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e,
                                                          HttpServletRequest request) {
        logError(request, e);
        return R.fail(String.valueOf(HttpStatus.METHOD_NOT_ALLOWED.value()), "请求方式 '%s' 不支持".formatted(e.getMethod()));
    }

    /**
     * 语音/文本输入异常处理
     */
    @ExceptionHandler(SpeechInputException.class)
    public R handleSpeechInputException(SpeechInputException e, HttpServletRequest request) {
        // 记录异常日志
        logError(request, e);
        // 返回标准响应结构，包含错误类型和友好提示
        Map<String, Object> data = new HashMap<>();
        data.put("errorType", e.getErrorType().name());
        data.put("characterType", e.getCharacterType());
        data.put("userTip", e.getUserTip());
        R<Object> objectR = new R<>();
        objectR.setCode(String.valueOf(HttpStatus.BAD_REQUEST.value()));
        objectR.setData(data);
        return objectR;
    }

    /**
     * 通用异常处理
     * 处理其他未预期的异常
     */
    @ExceptionHandler(Exception.class)
    public R handleException(Exception e, HttpServletRequest request) {
        logError(request, e);
        return R.fail(String.valueOf(HttpStatus.INTERNAL_SERVER_ERROR.value()), "系统异常，请联系管理员");
    }

    /**
     * 统一日志记录方法
     * 记录异常发生时的请求信息和异常堆栈
     */
    private void logError(HttpServletRequest request, Exception e) {
        Map<String, Object> errorLog = new HashMap<>();
        errorLog.put("type", "kid-err");
        errorLog.put("timestamp", DATE_FORMATTER.format(LocalDateTime.now()));
        if (UserContextHolder.getContext()!=null){
            errorLog.put("userId", UserContextHolder.getUserId());
        }
        errorLog.put("request", Map.of(
            "method", request.getMethod(),
            "uri", request.getRequestURI(),
            "ip", request.getRemoteAddr(),
            "userAgent", request.getHeader("User-Agent")
        ));
        // 记录请求参数和body
        String contentType = request.getContentType();
        if (contentType != null && contentType.toLowerCase().contains("application/json")) {
            // 读取JSON请求体
            try {
                // 兼容ContentCachingRequestWrapper
                Object body = null;
                if (request instanceof org.springframework.web.util.ContentCachingRequestWrapper wrapper) {
                    byte[] buf = wrapper.getContentAsByteArray();
                    if (buf.length > 0) {
                        body = new String(buf, wrapper.getCharacterEncoding());
                    }
                } else {
                    body = request.getReader().lines().reduce("", (a, b) -> a + b);
                }
                errorLog.put("body", body);
            } catch (Exception ex) {
                errorLog.put("body", "[读取body失败:" + ex.getMessage() + "]");
            }
        } else {
            // 普通表单参数
            Map<String, String[]> paramMap = request.getParameterMap();
            Map<String, Object> params = new HashMap<>();
            for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {
                params.put(entry.getKey(), entry.getValue().length == 1 ? entry.getValue()[0] : entry.getValue());
            }
            errorLog.put("params", JSONUtil.toJsonStr(params));
        }
        errorLog.put("error", Map.of(
            "type", e.getClass().getName(),
            "message", e.getMessage(),
            "stackTrace", CharSequenceUtil.join("\n", e.getStackTrace())
        ));
        errorLogger.error(JSONUtil.toJsonStr(errorLog));
    }
}